<?php
// Database configuration
$db_config = [
    'host' => '127.0.0.1',
    'username' => 'root',
    'password' => '',
    'database' => 'edlivkyhospital'
];

// Create database connection
function getDBConnection() {
    global $db_config;
    
    $connection = mysqli_connect(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    if (!$connection) {
        die("Connection failed: " . mysqli_connect_error());
    }
    
    // Set charset to utf8
    mysqli_set_charset($connection, "utf8");
    
    return $connection;
}

// Function to execute query and return result
function executeQuery($query, $params = []) {
    $connection = getDBConnection();
    
    if (!empty($params)) {
        $stmt = mysqli_prepare($connection, $query);
        if ($stmt) {
            // Determine types for bind_param
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } else {
                    $types .= 's';
                }
            }
            
            mysqli_stmt_bind_param($stmt, $types, ...$params);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            mysqli_stmt_close($stmt);
        } else {
            $result = false;
        }
    } else {
        $result = mysqli_query($connection, $query);
    }
    
    mysqli_close($connection);
    return $result;
}

// Function to get single record
function getSingleRecord($query, $params = []) {
    $result = executeQuery($query, $params);
    if ($result && mysqli_num_rows($result) > 0) {
        return mysqli_fetch_assoc($result);
    }
    return null;
}

// Function to get all records
function getAllRecords($query, $params = []) {
    $result = executeQuery($query, $params);
    $records = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $records[] = $row;
        }
    }
    return $records;
}

// Function to insert record and return ID
function insertRecord($query, $params = []) {
    $connection = getDBConnection();
    
    if (!empty($params)) {
        $stmt = mysqli_prepare($connection, $query);
        if ($stmt) {
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } else {
                    $types .= 's';
                }
            }
            
            mysqli_stmt_bind_param($stmt, $types, ...$params);
            $success = mysqli_stmt_execute($stmt);
            $insert_id = mysqli_insert_id($connection);
            mysqli_stmt_close($stmt);
        } else {
            $success = false;
            $insert_id = 0;
        }
    } else {
        $success = mysqli_query($connection, $query);
        $insert_id = mysqli_insert_id($connection);
    }
    
    mysqli_close($connection);
    return $success ? $insert_id : false;
}

// Function to update/delete records
function executeUpdate($query, $params = []) {
    $connection = getDBConnection();
    
    if (!empty($params)) {
        $stmt = mysqli_prepare($connection, $query);
        if ($stmt) {
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } else {
                    $types .= 's';
                }
            }
            
            mysqli_stmt_bind_param($stmt, $types, ...$params);
            $success = mysqli_stmt_execute($stmt);
            $affected_rows = mysqli_stmt_affected_rows($stmt);
            mysqli_stmt_close($stmt);
        } else {
            $success = false;
            $affected_rows = 0;
        }
    } else {
        $success = mysqli_query($connection, $query);
        $affected_rows = mysqli_affected_rows($connection);
    }
    
    mysqli_close($connection);
    return $success ? $affected_rows : false;
}

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Function to validate email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to validate phone number
function validatePhone($phone) {
    return preg_match('/^[\+]?[1-9][\d]{0,15}$/', $phone);
}

// Function to format date for database
function formatDateForDB($date) {
    if (empty($date)) return null;
    $dateTime = DateTime::createFromFormat('Y-m-d', $date);
    return $dateTime ? $dateTime->format('Y-m-d') : null;
}

// Function to format datetime for database
function formatDateTimeForDB($datetime) {
    if (empty($datetime)) return null;
    $dateTime = DateTime::createFromFormat('Y-m-d H:i', $datetime);
    return $dateTime ? $dateTime->format('Y-m-d H:i:s') : null;
}
?>
