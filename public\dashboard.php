<?php
$pageTitle = "Dashboard";
include 'includes/header.php';
?>

<?php include 'includes/sidebar.php'; ?>

<div class="p-4 sm:ml-64">
    <div class="p-4 border-2 border-gray-200 border-dashed rounded-lg dark:border-gray-700 mt-14">
        
        <!-- Dashboard Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
            <p class="text-gray-600 dark:text-gray-400">Welcome to EdlivkyHospital Administration</p>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-plus-circle me-2"></i>Quick Actions
            </h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Register Patient -->
                <a href="patients/add.php" class="quick-action-btn bg-blue-500 hover:bg-blue-600">
                    <i class="fas fa-user-plus text-2xl mb-2"></i>
                    <span>Register Patient</span>
                </a>
                
                <!-- Schedule Appointment -->
                <a href="appointments/add.php" class="quick-action-btn bg-green-500 hover:bg-green-600">
                    <i class="fas fa-calendar-plus text-2xl mb-2"></i>
                    <span>Schedule Appointment</span>
                </a>
                
                <!-- Add Medical Record -->
                <a href="medical-records/add.php" class="quick-action-btn bg-purple-500 hover:bg-purple-600">
                    <i class="fas fa-file-medical text-2xl mb-2"></i>
                    <span>Add Medical Record</span>
                </a>
                
                <!-- Assign Bed -->
                <a href="beds/add.php" class="quick-action-btn bg-orange-500 hover:bg-orange-600">
                    <i class="fas fa-bed text-2xl mb-2"></i>
                    <span>Assign Bed</span>
                </a>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- Total Patients -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Patients</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">1,247</p>
                        <p class="text-sm text-green-600 dark:text-green-400">
                            <i class="fas fa-arrow-up me-1"></i>+12% from last month
                        </p>
                    </div>
                    <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                        <i class="fas fa-users text-blue-600 dark:text-blue-300 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Active Staff -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Staff</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">89</p>
                        <p class="text-sm text-green-600 dark:text-green-400">
                            <i class="fas fa-arrow-up me-1"></i>+5% from last month
                        </p>
                    </div>
                    <div class="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                        <i class="fas fa-user-md text-green-600 dark:text-green-300 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Today's Appointments -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Today's Appointments</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">23</p>
                        <p class="text-sm text-green-600 dark:text-green-400">
                            <i class="fas fa-arrow-up me-1"></i>+8% from last month
                        </p>
                    </div>
                    <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                        <i class="fas fa-calendar-check text-purple-600 dark:text-purple-300 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Available Beds -->
            <div class="stat-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Available Beds</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">42</p>
                        <p class="text-sm text-red-600 dark:text-red-400">
                            <i class="fas fa-arrow-down me-1"></i>-3% from last month
                        </p>
                    </div>
                    <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
                        <i class="fas fa-bed text-orange-600 dark:text-orange-300 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Monthly Hospital Activity Chart -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-chart-bar me-2"></i>Monthly Hospital Activity
                    </h3>
                </div>
                <div class="relative h-80">
                    <canvas id="monthlyActivityChart"></canvas>
                </div>
            </div>

            <!-- Patient Distribution by Department -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-chart-pie me-2"></i>Patient Distribution by Department
                    </h3>
                </div>
                <div class="relative h-80">
                    <canvas id="departmentChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="chart-container">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
            <div class="space-y-4">
                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="w-2 h-2 bg-blue-500 rounded-full me-3"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">New patient registered</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">2 minutes ago</p>
                    </div>
                </div>
                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="w-2 h-2 bg-green-500 rounded-full me-3"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Appointment scheduled</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">5 minutes ago</p>
                    </div>
                </div>
                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="w-2 h-2 bg-purple-500 rounded-full me-3"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Medical record updated</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">10 minutes ago</p>
                    </div>
                </div>
                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="w-2 h-2 bg-orange-500 rounded-full me-3"></div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Bed assigned to patient</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">15 minutes ago</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Charts Script -->
<script src="assets/js/dashboard-charts.js"></script>

<?php include 'includes/footer.php'; ?>
