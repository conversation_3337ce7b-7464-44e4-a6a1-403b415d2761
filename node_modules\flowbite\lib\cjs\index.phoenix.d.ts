import Accordion from './components/accordion';
import Carousel from './components/carousel';
import Collapse from './components/collapse';
import Dial from './components/dial';
import Dismiss from './components/dismiss';
import Drawer from './components/drawer';
import Dropdown from './components/dropdown';
import Modal from './components/modal';
import Popover from './components/popover';
import Tabs from './components/tabs';
import Tooltip from './components/tooltip';
import InputCounter from './components/input-counter';
import CopyClipboard from './components/clipboard';
import Datepicker from './components/datepicker';
import './components/index';
import Events from './dom/events';
declare const _default: {
    Accordion: typeof Accordion;
    Carousel: typeof Carousel;
    Collapse: typeof Collapse;
    Dial: typeof Dial;
    Drawer: typeof Drawer;
    Dismiss: typeof Dismiss;
    Dropdown: typeof Dropdown;
    Modal: typeof Modal;
    Popover: typeof Popover;
    Tabs: typeof Tabs;
    Tooltip: typeof Tooltip;
    InputCounter: typeof InputCounter;
    CopyClipboard: typeof CopyClipboard;
    Datepicker: typeof Datepicker;
    Events: typeof Events;
};
export default _default;
//# sourceMappingURL=index.phoenix.d.ts.map