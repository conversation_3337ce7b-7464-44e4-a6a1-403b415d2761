{"name": "flatpickr", "version": "4.6.13", "description": "A lightweight, powerful javascript datetime picker", "scripts": {"build": "run-s build:pre build:build build:esm build:types build:post", "build:pre": "<PERSON><PERSON><PERSON> dist", "build:build": "ts-node --transpile-only build.ts", "build:esm": "tsc -p tsconfig.esm.json", "build:types": "tsc -p tsconfig.declarations.json", "build:post": "cp src/typings.d.ts dist/typings.d.ts", "fmt": "prettier --ignore-path .gitignore --trailing-comma es5 --write \"**/*.ts\"", "fmt:check": "prettier --ignore-path .gitignore --trailing-comma es5 --check \"**/*.ts\"", "start": "npm run build:build -- --dev", "test": "run-s test:typecheck test:unit", "test:typecheck": "tsc --noEmit --incremental", "test:unit": "jest --config config/jest.json", "coveralls": "npm run test:unit -- --coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-proposal-optional-catch-binding": "^7.12.1", "@babel/preset-env": "^7.12.11", "@types/acorn": "^4.0.5", "@types/fs-extra": "^9.0.5", "@types/glob": "7.1.3", "@types/jest": "^27.4.1", "@types/jquery": "^3.5.5", "@types/node": "^14.14.14", "@types/stylus": "^0.48.33", "autoprefixer-stylus": "latest", "babel-plugin-transform-object-rest-spread": "^6.26.0", "chokidar": "^3.4.3", "coveralls": "^3.1.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "jest": "^27.5.1", "npm-run-all": ">=4.1.5", "prettier": "^2.2.1", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-typescript": "^1.0.1", "stylus": "latest", "terser": "^5.5.1", "ts-jest": "^27.1.3", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "^4.1.3"}, "main": "dist/flatpickr.js", "module": "dist/esm/index.js", "style": "dist/flatpickr.css", "types": "./dist/typings.d.ts", "repository": {"type": "git", "url": "git+https://github.com/chmln/flatpickr.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/chmln/flatpickr/issues"}, "homepage": "https://flatpickr.js.org", "keywords": ["javascript", "datetimepicker", "calendar", "date", "time", "picker", "lightweight"], "browserslist": ["ie >= 9", "last 2 versions", "safari >= 7"]}